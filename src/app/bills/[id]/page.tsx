"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { Navbar } from "@/components/layout/navbar"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { formatCurrency, formatDate } from "@/lib/utils"
import { useToast } from "@/hooks/use-toast"
import {
  ArrowLeft,
  Receipt,
  Users,
  CheckCircle,
  Clock,
  User,
  DollarSign
} from "lucide-react"
import {useSession} from "next-auth/react";

interface BillItem {
  name: string
  price: number
  consumers: {
    _id: string;
    portions: number;
    user: {
      name: string;
      email: string;
      _id: string;
    }
  }[]
}

interface Participant {
  user: { name: string; _id: string }
  amountOwed: number
  status: "paid" | "unpaid"
}

interface Bill {
  _id: string
  title: string
  total: number
  items: BillItem[]
  participants: Participant[]
  creator: { name: string; _id: string }
  createdAt: string
}

export default function BillDetailPage() {
  const { data: session } = useSession()
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const [bill, setBill] = useState<Bill | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [updatingPayment, setUpdatingPayment] = useState<string | null>(null)

  useEffect(() => {
    const fetchBill = async () => {
      try {
        const response = await fetch(`/api/bills/${params.id}`)

        if (response.ok) {
          const data = await response.json()
          setBill(data.bill)
        } else {
          toast({
            title: "Ошибка",
            description: "Не удалось загрузить счет",
            variant: "destructive"
          })
        }
      } catch {
        toast({
          title: "Ошибка",
          description: "Не удалось загрузить счет",
          variant: "destructive"
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchBill()
  }, [params.id, toast])

  const markAsPaid = async (participantId: string) => {
    setUpdatingPayment(participantId)

    try {
      const response = await fetch(`/api/bills/${params.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          participantId,
          status: "paid"
        })
      })

      if (response.ok) {
        const data = await response.json()
        setBill(data.bill)

        toast({
          title: "Платеж подтвержден",
          description: "Статус платежа обновлен"
        })
      } else {
        toast({
          title: "Ошибка",
          description: "Не удалось обновить статус",
          variant: "destructive"
        })
      }
    } catch {
      toast({
        title: "Ошибка",
        description: "Не удалось обновить статус",
        variant: "destructive"
      })
    } finally {
      setUpdatingPayment(null)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Загрузка счета...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!bill) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Счет не найден</h1>
            <Button onClick={() => router.push("/dashboard")}>
              Вернуться к счетам
            </Button>
          </div>
        </div>
      </div>
    )
  }

  const totalPaid = bill.participants
    .filter(p => p.status === "paid")
    .reduce((sum, p) => sum + p.amountOwed, 0)

  const totalUnpaid = bill.participants
    .filter(p => p.status === "unpaid")
    .reduce((sum, p) => sum + p.amountOwed, 0)

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100">
      <Navbar />

      <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-10 gap-4">
            <div className="flex items-center">
              <Button
                variant="outline"
                onClick={() => router.back()}
                className="mr-6 border-0 bg-white/80 hover:bg-white shadow-md"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Назад
              </Button>
              <div>
                <h1 className="text-4xl font-bold text-gray-900 mb-2">{bill.title}</h1>
                <p className="text-lg text-gray-600">
                  Создан {formatDate(new Date(bill.createdAt))} • {formatCurrency(bill.total)}
                </p>
              </div>
            </div>

            {/* Статус счета */}
            <div className="flex items-center space-x-3">
              {totalUnpaid === 0 ? (
                <div className="flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-xl font-semibold shadow-sm">
                  <CheckCircle className="h-5 w-5 mr-2" />
                  Полностью оплачен
                </div>
              ) : (
                <div className="flex items-center px-4 py-2 bg-orange-100 text-orange-800 rounded-xl font-semibold shadow-sm">
                  <Clock className="h-5 w-5 mr-2" />
                  Ожидает оплаты
                </div>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Основная информация */}
            <div className="lg:col-span-2 space-y-6">
              {/* Позиции счета */}
              <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center text-xl">
                    <Receipt className="h-6 w-6 mr-3 text-blue-600" />
                    Позиции счета
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-2">
                  <div className="space-y-6">
                    {bill.items.map((item, index) => (
                      <div key={index} className="p-6 rounded-xl bg-gray-50/50 hover:bg-gray-100/50 transition-colors border-0 shadow-sm">
                        <div className="flex justify-between items-start mb-4">
                          <h4 className="text-lg font-semibold text-gray-900">{item.name}</h4>
                          <div className="text-right">
                            <span className="text-2xl font-bold text-gray-900">
                              {formatCurrency(item.price)}
                            </span>
                            <p className="text-sm text-gray-600 mt-1">за позицию</p>
                          </div>
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {item.consumers.map((consumer, idx) => (
                            <span
                              key={idx}
                              className="inline-flex items-center px-3 py-2 rounded-xl text-sm bg-blue-100 text-blue-800 font-medium shadow-sm"
                            >
                              <User className="h-4 w-4 mr-2" />
                              {consumer.user.name}
                            </span>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Участники */}
              <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center text-xl">
                    <Users className="h-6 w-6 mr-3 text-purple-600" />
                    Участники и платежи
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-2">
                  <div className="space-y-4">
                    {bill.participants.map((participant) => (
                      <div
                        key={participant.user._id}
                        className="flex items-center justify-between p-6 rounded-xl bg-gray-50/50 hover:bg-gray-100/50 transition-colors border-0 shadow-sm"
                      >
                        <div className="flex items-center space-x-4">
                          <div className={`p-3 rounded-xl shadow-sm ${
                            participant.status === 'paid'
                              ? 'bg-green-100'
                              : 'bg-orange-100'
                          }`}>
                            {participant.status === 'paid' ? (
                              <CheckCircle className="h-5 w-5 text-green-600" />
                            ) : (
                              <Clock className="h-5 w-5 text-orange-600" />
                            )}
                          </div>
                          <div>
                            <p className="text-lg font-semibold text-gray-900">
                              {participant.user.name}
                            </p>
                            <p className="text-base text-gray-600 font-medium">
                              {formatCurrency(participant.amountOwed)}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center space-x-3">
                          <span className={`px-4 py-2 rounded-xl text-sm font-semibold shadow-sm ${
                            participant.status === 'paid'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-orange-100 text-orange-800'
                          }`}>
                            {participant.status === 'paid' ? 'Оплачено' : 'Ожидает оплаты'}
                          </span>

                          {participant.status === 'unpaid' && session?.user?.id === bill.creator._id && (
                            <Button
                              size="sm"
                              onClick={() => markAsPaid(participant.user._id)}
                              disabled={updatingPayment === participant.user._id}
                              className="bg-green-600 hover:bg-green-700 border-0 shadow-lg px-4 py-2"
                            >
                              {updatingPayment === participant.user._id ? (
                                "Обновление..."
                              ) : (
                                "Отметить как оплачено"
                              )}
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Боковая панель */}
            <div className="space-y-6">
              {/* Сводка */}
              <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white border-0 shadow-xl">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center text-xl">
                    <DollarSign className="h-6 w-6 mr-3 opacity-90" />
                    Сводка
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6 pt-2">
                  <div className="flex justify-between items-center">
                    <span className="text-blue-100 font-medium">Общая сумма:</span>
                    <span className="text-2xl font-bold">{formatCurrency(bill.total)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-blue-100 font-medium">Оплачено:</span>
                    <span className="text-xl font-bold text-green-200">
                      {formatCurrency(totalPaid)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-blue-100 font-medium">Осталось:</span>
                    <span className="text-xl font-bold text-orange-200">
                      {formatCurrency(totalUnpaid)}
                    </span>
                  </div>
                  <div className="pt-4 border-t border-blue-400/30">
                    <div className="w-full bg-blue-400/30 rounded-full h-3 shadow-inner">
                      <div
                        className="bg-gradient-to-r from-green-400 to-green-300 h-3 rounded-full transition-all duration-500 shadow-sm"
                        style={{ width: `${(totalPaid / bill.total) * 100}%` }}
                      ></div>
                    </div>
                    <p className="text-sm text-blue-100 mt-3 text-center font-medium">
                      {Math.round((totalPaid / bill.total) * 100)}% оплачено
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Информация о создателе */}
              <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center text-xl">
                    <User className="h-6 w-6 mr-3 text-indigo-600" />
                    Информация
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6 pt-2">
                  <div className="p-4 rounded-xl bg-gray-50/50">
                    <p className="text-sm text-gray-600 font-medium mb-2">Создатель:</p>
                    <p className="text-lg font-semibold text-gray-900">{bill.creator.name}</p>
                  </div>
                  <div className="p-4 rounded-xl bg-gray-50/50">
                    <p className="text-sm text-gray-600 font-medium mb-2">Дата создания:</p>
                    <p className="text-lg font-semibold text-gray-900">{formatDate(new Date(bill.createdAt))}</p>
                  </div>
                  <div className="p-4 rounded-xl bg-gray-50/50">
                    <p className="text-sm text-gray-600 font-medium mb-2">Участников:</p>
                    <p className="text-lg font-semibold text-gray-900">{bill.participants.length}</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
      </div>
    </div>
  )
}
