"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { Navbar } from "@/components/layout/navbar"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { formatCurrency, formatDate } from "@/lib/utils"
import { useToast } from "@/hooks/use-toast"
import {
  ArrowLeft,
  Receipt,
  Users,
  CheckCircle,
  Clock,
  User,
  DollarSign
} from "lucide-react"
import {useSession} from "next-auth/react";

interface BillItem {
  name: string
  price: number
  consumers: {
    _id: string;
    portions: number;
    user: {
      name: string;
      email: string;
      _id: string;
    }
  }[]
}

interface Participant {
  user: { name: string; _id: string }
  amountOwed: number
  status: "paid" | "unpaid"
}

interface Bill {
  _id: string
  title: string
  total: number
  items: BillItem[]
  participants: Participant[]
  creator: { name: string; _id: string }
  createdAt: string
}

export default function BillDetailPage() {
  const { data: session } = useSession()
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const [bill, setBill] = useState<Bill | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [updatingPayment, setUpdatingPayment] = useState<string | null>(null)

  useEffect(() => {
    const fetchBill = async () => {
      try {
        const response = await fetch(`/api/bills/${params.id}`)

        if (response.ok) {
          const data = await response.json()
          setBill(data.bill)
        } else {
          toast({
            title: "Ошибка",
            description: "Не удалось загрузить счет",
            variant: "destructive"
          })
        }
      } catch {
        toast({
          title: "Ошибка",
          description: "Не удалось загрузить счет",
          variant: "destructive"
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchBill()
  }, [params.id, toast])

  const markAsPaid = async (participantId: string) => {
    setUpdatingPayment(participantId)

    try {
      const response = await fetch(`/api/bills/${params.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          participantId,
          status: "paid"
        })
      })

      if (response.ok) {
        const data = await response.json()
        setBill(data.bill)

        toast({
          title: "Платеж подтвержден",
          description: "Статус платежа обновлен"
        })
      } else {
        toast({
          title: "Ошибка",
          description: "Не удалось обновить статус",
          variant: "destructive"
        })
      }
    } catch {
      toast({
        title: "Ошибка",
        description: "Не удалось обновить статус",
        variant: "destructive"
      })
    } finally {
      setUpdatingPayment(null)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Загрузка счета...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!bill) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Счет не найден</h1>
            <Button onClick={() => router.push("/dashboard")}>
              Вернуться к счетам
            </Button>
          </div>
        </div>
      </div>
    )
  }

  const totalPaid = bill.participants
    .filter(p => p.status === "paid")
    .reduce((sum, p) => sum + p.amountOwed, 0)

  const totalUnpaid = bill.participants
    .filter(p => p.status === "unpaid")
    .reduce((sum, p) => sum + p.amountOwed, 0)

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100">
      <Navbar />

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="flex items-center mb-8">
            <Button
              variant="ghost"
              onClick={() => router.back()}
              className="mr-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Назад
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{bill.title}</h1>
              <p className="text-gray-600 mt-1">
                Создан {formatDate(new Date(bill.createdAt))} • {formatCurrency(bill.total)}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Основная информация */}
            <div className="lg:col-span-2 space-y-6">
              {/* Позиции счета */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Receipt className="h-5 w-5 mr-2" />
                    Позиции счета
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {bill.items.map((item, index) => (
                      <div key={index} className="border-b pb-4 last:border-b-0">
                        <div className="flex justify-between items-start mb-2">
                          <h4 className="font-medium text-gray-900">{item.name}</h4>
                          <span className="font-semibold text-gray-900">
                            {formatCurrency(item.price)}
                          </span>
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {item.consumers.map((consumer, idx) => (
                            <span
                              key={idx}
                              className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800"
                            >
                              <User className="h-3 w-3 mr-1" />
                              {consumer.user.name}
                            </span>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Участники */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Users className="h-5 w-5 mr-2" />
                    Участники и платежи
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {bill.participants.map((participant) => (
                      <div
                        key={participant.user._id}
                        className="flex items-center justify-between p-4 border rounded-lg"
                      >
                        <div className="flex items-center space-x-3">
                          <div className={`p-2 rounded-full ${
                            participant.status === 'paid'
                              ? 'bg-green-100'
                              : 'bg-orange-100'
                          }`}>
                            {participant.status === 'paid' ? (
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            ) : (
                              <Clock className="h-4 w-4 text-orange-600" />
                            )}
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">
                              {participant.user.name}
                            </p>
                            <p className="text-sm text-gray-600">
                              {formatCurrency(participant.amountOwed)}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                            participant.status === 'paid'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-orange-100 text-orange-800'
                          }`}>
                            {participant.status === 'paid' ? 'Оплачено' : 'Ожидает оплаты'}
                          </span>

                          {participant.status === 'unpaid' && session?.user?.id === bill.creator._id && (
                            <Button
                              size="sm"
                              onClick={() => markAsPaid(participant.user._id)}
                              disabled={updatingPayment === participant.user._id}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              {updatingPayment === participant.user._id ? (
                                "Обновление..."
                              ) : (
                                "Отметить как оплачено"
                              )}
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Боковая панель */}
            <div className="space-y-6">
              {/* Сводка */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <DollarSign className="h-5 w-5 mr-2" />
                    Сводка
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Общая сумма:</span>
                    <span className="font-semibold">{formatCurrency(bill.total)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Оплачено:</span>
                    <span className="font-semibold text-green-600">
                      {formatCurrency(totalPaid)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Осталось:</span>
                    <span className="font-semibold text-orange-600">
                      {formatCurrency(totalUnpaid)}
                    </span>
                  </div>
                  <div className="pt-2 border-t">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(totalPaid / bill.total) * 100}%` }}
                      ></div>
                    </div>
                    <p className="text-sm text-gray-600 mt-1 text-center">
                      {Math.round((totalPaid / bill.total) * 100)}% оплачено
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Информация о создателе */}
              <Card>
                <CardHeader>
                  <CardTitle>Информация</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-600">Создатель:</p>
                    <p className="font-medium">{bill.creator.name}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Дата создания:</p>
                    <p className="font-medium">{formatDate(new Date(bill.createdAt))}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Участников:</p>
                    <p className="font-medium">{bill.participants.length}</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
