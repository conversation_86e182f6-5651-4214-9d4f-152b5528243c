"use client"

import { useState, useEffect } from "react"
import { Navbar } from "@/components/layout/navbar"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import {
  Search,
  UserPlus,
  Users,
  Check,
  X,
  Mail,
  Clock,
  UserCheck
} from "lucide-react"

interface Friend {
  _id: string
  name: string
  email: string
}

interface SearchResult {
  _id: string
  name: string
  email: string
}

interface FriendRequest {
  _id: string
  from: { _id: string; name: string; email: string }
  to: { _id: string; name: string; email: string }
  status: "pending" | "accepted" | "rejected"
}

export default function FriendsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])
  const [friends, setFriends] = useState<Friend[]>([])
  const [sentRequests, setSentRequests] = useState<FriendRequest[]>([])
  const [receivedRequests, setReceivedRequests] = useState<FriendRequest[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const { toast } = useToast()

  useEffect(() => {
    const fetchFriendsData = async () => {
      try {
        const response = await fetch("/api/friends")
        if (response.ok) {
          const data = await response.json()
          setFriends(data.friends || [])
          setSentRequests(data.sentRequests || [])
          setReceivedRequests(data.receivedRequests || [])
        }
      } catch (error) {
        console.error("Error fetching friends data:", error)
        toast({
          title: "Ошибка",
          description: "Не удалось загрузить данные о друзьях",
          variant: "destructive"
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchFriendsData()
  }, [])

  const handleSearch = async () => {
    if (!searchQuery.trim()) return

    setIsSearching(true)

    try {
      const response = await fetch(`/api/users/search?q=${encodeURIComponent(searchQuery)}`)
      if (response.ok) {
        const data = await response.json()
        setSearchResults(data.users || [])
      } else {
        toast({
          title: "Ошибка поиска",
          description: "Не удалось найти пользователей",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Ошибка",
        description: "Не удалось выполнить поиск",
        variant: "destructive"
      })
    } finally {
      setIsSearching(false)
    }
  }

  const sendFriendRequest = async (userId: string) => {
    try {
      const response = await fetch("/api/friends/request", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ userId })
      })

      if (response.ok) {
        toast({
          title: "Заявка отправлена",
          description: "Заявка в друзья успешно отправлена"
        })

        // Удаляем из результатов поиска
        setSearchResults(prev => prev.filter(user => user._id !== userId))
      } else {
        const data = await response.json()
        toast({
          title: "Ошибка",
          description: data.error || "Не удалось отправить заявку",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Ошибка",
        description: "Не удалось отправить заявку",
        variant: "destructive"
      })
    }
  }

  const acceptFriendRequest = async (requestId: string) => {
    try {
      const response = await fetch("/api/friends/request", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ requestId, action: "accept" })
      })

      if (response.ok) {
        toast({
          title: "Заявка принята",
          description: "Пользователь добавлен в друзья"
        })

        // Обновляем данные
        const fetchResponse = await fetch("/api/friends")
        if (fetchResponse.ok) {
          const data = await fetchResponse.json()
          setFriends(data.friends || [])
          setReceivedRequests(data.receivedRequests || [])
        }
      } else {
        const data = await response.json()
        toast({
          title: "Ошибка",
          description: data.error || "Не удалось принять заявку",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Ошибка",
        description: "Не удалось принять заявку",
        variant: "destructive"
      })
    }
  }

  const rejectFriendRequest = async (requestId: string) => {
    try {
      const response = await fetch("/api/friends/request", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ requestId, action: "reject" })
      })

      if (response.ok) {
        toast({
          title: "Заявка отклонена",
          description: "Заявка в друзья отклонена"
        })

        // Удаляем из списка входящих заявок
        setReceivedRequests(prev => prev.filter(req => req._id !== requestId))
      } else {
        const data = await response.json()
        toast({
          title: "Ошибка",
          description: data.error || "Не удалось отклонить заявку",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Ошибка",
        description: "Не удалось отклонить заявку",
        variant: "destructive"
      })
    }
  }

  const removeFriend = async (userId: string) => {
    try {
      const response = await fetch("/api/friends/remove", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ userId })
      })

      if (response.ok) {
        setFriends(prev => prev.filter(friend => friend._id !== userId))
        toast({
          title: "Друг удален",
          description: "Пользователь удален из списка друзей"
        })
      } else {
        const data = await response.json()
        toast({
          title: "Ошибка",
          description: data.error || "Не удалось удалить друга",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Ошибка",
        description: "Не удалось удалить друга",
        variant: "destructive"
      })
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <div className="relative">
                <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto"></div>
                <div className="absolute inset-0 rounded-full h-16 w-16 border-4 border-transparent border-r-purple-400 animate-spin mx-auto" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
              </div>
              <p className="mt-6 text-lg text-gray-600 font-medium">Загрузка друзей...</p>
              <p className="mt-2 text-sm text-gray-500">Получаем ваши контакты</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100">
      <Navbar />

      <div className="container mx-auto px-4 py-8">
          <div className="mb-10">
            <h1 className="text-4xl font-bold text-gray-900 mb-2">Друзья</h1>
            <p className="text-lg text-gray-600">
              Управляйте своими друзьями и отправляйте заявки новым пользователям
            </p>
          </div>

          {/* Поиск пользователей */}
          <Card className="mb-8 border-0 shadow-lg bg-white/80 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center text-xl">
                <Search className="h-6 w-6 mr-3 text-blue-600" />
                Найти друзей
              </CardTitle>
              <CardDescription className="text-gray-600">
                Найдите пользователей по имени или email
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-2">
              <div className="flex flex-col sm:flex-row gap-4">
                <Input
                  placeholder="Введите имя или email..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="flex-1 py-3 border-0 bg-gray-50/50 focus:bg-white focus:ring-2 focus:ring-blue-500 rounded-xl"
                />
                <Button
                  onClick={handleSearch}
                  disabled={isSearching || !searchQuery.trim()}
                  className="bg-blue-600 hover:bg-blue-700 border-0 shadow-lg px-8 py-3"
                >
                  {isSearching ? "Поиск..." : "Найти"}
                </Button>
              </div>

              {/* Результаты поиска */}
              {searchResults.length > 0 && (
                <div className="mt-6 space-y-4">
                  <h4 className="font-semibold text-gray-900 text-lg">Результаты поиска:</h4>
                  {searchResults.map(user => (
                    <div
                      key={user._id}
                      className="flex items-center justify-between p-4 rounded-xl bg-gray-50/50 hover:bg-gray-100/50 transition-colors border-0 shadow-sm"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                          <UserPlus className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-semibold text-gray-900">{user.name}</p>
                          <p className="text-sm text-gray-600">{user.email}</p>
                        </div>
                      </div>
                      <Button
                        size="sm"
                        onClick={() => sendFriendRequest(user._id)}
                        className="bg-blue-600 hover:bg-blue-700 border-0 shadow-md px-6 py-2"
                      >
                        <UserPlus className="h-4 w-4 mr-2" />
                        Добавить
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Входящие заявки */}
          {receivedRequests.length > 0 && (
            <Card className="mb-8 border-0 shadow-lg bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center text-xl">
                  <Mail className="h-6 w-6 mr-3 text-green-600" />
                  Входящие заявки ({receivedRequests.length})
                </CardTitle>
                <CardDescription className="text-gray-600">
                  Пользователи, которые хотят добавить вас в друзья
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-2">
                <div className="space-y-4">
                  {receivedRequests.map(request => (
                    <div
                      key={request._id}
                      className="flex items-center justify-between p-4 rounded-xl bg-gradient-to-r from-green-50 to-green-100/50 border border-green-100/50 shadow-sm"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                          <Mail className="h-6 w-6 text-green-600" />
                        </div>
                        <div>
                          <p className="font-semibold text-gray-900">{request.from.name}</p>
                          <p className="text-sm text-gray-600">{request.from.email}</p>
                        </div>
                      </div>
                      <div className="flex space-x-3">
                        <Button
                          size="sm"
                          onClick={() => acceptFriendRequest(request._id)}
                          className="bg-green-600 hover:bg-green-700 border-0 shadow-md px-4 py-2"
                        >
                          <Check className="h-4 w-4 mr-2" />
                          Принять
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => rejectFriendRequest(request._id)}
                          className="border-0 bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700 px-4 py-2"
                        >
                          <X className="h-4 w-4 mr-2" />
                          Отклонить
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Отправленные заявки */}
          {sentRequests.length > 0 && (
            <Card className="mb-8 border-0 shadow-lg bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center text-xl">
                  <Clock className="h-6 w-6 mr-3 text-orange-600" />
                  Отправленные заявки ({sentRequests.length})
                </CardTitle>
                <CardDescription className="text-gray-600">
                  Заявки, которые вы отправили и которые ожидают ответа
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-2">
                <div className="space-y-4">
                  {sentRequests.map(request => (
                    <div
                      key={request._id}
                      className="flex items-center justify-between p-4 rounded-xl bg-gradient-to-r from-orange-50 to-orange-100/50 border border-orange-100/50 shadow-sm"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                          <Clock className="h-6 w-6 text-orange-600" />
                        </div>
                        <div>
                          <p className="font-semibold text-gray-900">{request.to.name}</p>
                          <p className="text-sm text-gray-600">{request.to.email}</p>
                        </div>
                      </div>
                      <span className="px-4 py-2 rounded-full text-sm font-semibold bg-orange-100 text-orange-800 border border-orange-200">
                        Ожидает ответа
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Список друзей */}
          <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center text-xl">
                <Users className="h-6 w-6 mr-3 text-purple-600" />
                Мои друзья ({friends.length})
              </CardTitle>
              <CardDescription className="text-gray-600">
                Ваши подтвержденные друзья
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-2">
              {friends.length === 0 ? (
                <div className="text-center py-16">
                  <Users className="h-20 w-20 text-gray-300 mx-auto mb-6" />
                  <h3 className="text-2xl font-medium text-gray-900 mb-3">
                    Пока нет друзей
                  </h3>
                  <p className="text-gray-600 max-w-md mx-auto">
                    Найдите и добавьте друзей, чтобы начать делиться счетами
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {friends.map(friend => (
                    <div
                      key={friend._id}
                      className="flex items-center justify-between p-4 rounded-xl bg-gray-50/50 hover:bg-gray-100/50 transition-colors border-0 shadow-sm"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                          <UserCheck className="h-6 w-6 text-green-600" />
                        </div>
                        <div>
                          <p className="font-semibold text-gray-900">{friend.name}</p>
                          <p className="text-sm text-gray-600">{friend.email}</p>
                        </div>
                      </div>

                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => removeFriend(friend._id)}
                        className="border-0 bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700 px-4 py-2"
                      >
                        Удалить
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
