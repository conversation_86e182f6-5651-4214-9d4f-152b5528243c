"use client"

import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { Navbar } from "@/components/layout/navbar"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { formatCurrency } from "@/lib/utils"
import { useToast } from "@/hooks/use-toast"
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell
} from "recharts"
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Calendar,
  Users,
  Receipt,
  BarChart3,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Activity
} from "lucide-react"
import { format, parseISO } from "date-fns"
import { ru } from "date-fns/locale"

interface Analytics {
  period: string
  totalSpent: number
  totalOwed: number
  totalOwing: number
  billsCreated: number
  billsParticipated: number
  avgDailySpending: number
  spendingChange: number
  dailySpending: Array<{ date: string; amount: number }>
  topFriends: Array<{ name: string; amount: number }>
  categoryData: Array<{ category: string; amount: number }>
  totalBills: number
}

const PERIOD_OPTIONS = [
  { value: '1week', label: 'Неделя' },
  { value: '1month', label: 'Месяц' },
  { value: '3months', label: '3 месяца' },
  { value: '6months', label: '6 месяцев' },
  { value: '1year', label: 'Год' },
  { value: 'all', label: 'Все время' }
]

const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4']

export default function AnalyticsPage() {
  const { data: session } = useSession()
  const { toast } = useToast()
  const [analytics, setAnalytics] = useState<Analytics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [selectedPeriod, setSelectedPeriod] = useState('1month')

  const fetchAnalytics = async (period: string) => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/analytics?period=${period}`)
      if (response.ok) {
        const data = await response.json()
        setAnalytics(data.analytics)
      } else {
        toast({
          title: "Ошибка",
          description: "Не удалось загрузить аналитику",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error fetching analytics:", error)
      toast({
        title: "Ошибка",
        description: "Не удалось загрузить аналитику",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (session?.user?.id) {
      fetchAnalytics(selectedPeriod)
    }
  }, [session, selectedPeriod])

  const handlePeriodChange = (period: string) => {
    setSelectedPeriod(period)
  }

  const formatTooltipDate = (dateString: string) => {
    try {
      return format(parseISO(dateString), 'dd MMM yyyy', { locale: ru })
    } catch {
      return dateString
    }
  }

  const getSpendingTrend = () => {
    if (!analytics) return { icon: Activity, color: 'text-gray-600', text: 'Нет данных' }

    if (analytics.spendingChange > 0) {
      return {
        icon: TrendingUp,
        color: 'text-red-600',
        text: `+${analytics.spendingChange.toFixed(1)}% к предыдущему периоду`
      }
    } else if (analytics.spendingChange < 0) {
      return {
        icon: TrendingDown,
        color: 'text-green-600',
        text: `${analytics.spendingChange.toFixed(1)}% к предыдущему периоду`
      }
    } else {
      return {
        icon: Activity,
        color: 'text-gray-600',
        text: 'Без изменений'
      }
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <div className="relative">
                <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto"></div>
                <div className="absolute inset-0 rounded-full h-16 w-16 border-4 border-transparent border-r-purple-400 animate-spin mx-auto" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
              </div>
              <p className="mt-6 text-lg text-gray-600 font-medium">Загрузка аналитики...</p>
              <p className="mt-2 text-sm text-gray-500">Анализируем ваши данные</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!analytics) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Activity className="h-10 w-10 text-red-500" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-4">Ошибка загрузки</h1>
              <p className="text-gray-600 mb-6">Не удалось загрузить данные аналитики</p>
              <Button
                onClick={() => fetchAnalytics(selectedPeriod)}
                className="bg-blue-600 hover:bg-blue-700 border-0 shadow-lg px-8 py-3"
              >
                Попробовать снова
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const trend = getSpendingTrend()

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100">
      <Navbar />

      <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-10 gap-4">
            <div>
              <h1 className="text-4xl font-bold text-gray-900 mb-2">Аналитика расходов</h1>
              <p className="text-lg text-gray-600">
                Анализ ваших трат и финансовых привычек
              </p>
            </div>

            {/* Period Selector */}
            <div className="flex flex-wrap gap-2">
              {PERIOD_OPTIONS.map((option) => (
                <Button
                  key={option.value}
                  variant={selectedPeriod === option.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePeriodChange(option.value)}
                  className={selectedPeriod === option.value
                    ? "bg-blue-600 hover:bg-blue-700 border-0 shadow-lg"
                    : "border-0 bg-white/80 hover:bg-white shadow-md"
                  }
                >
                  {option.label}
                </Button>
              ))}
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10">
            <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white border-0 shadow-xl">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-medium opacity-90">Всего потрачено</CardTitle>
                <DollarSign className="h-5 w-5 opacity-90" />
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold mb-2">{formatCurrency(analytics.totalSpent)}</div>
                <div className="flex items-center text-xs opacity-90">
                  <trend.icon className="h-3 w-3 mr-1" />
                  {trend.text}
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-medium text-gray-700">Средние траты в день</CardTitle>
                <Calendar className="h-5 w-5 text-orange-500" />
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-gray-900 mb-2">{formatCurrency(analytics.avgDailySpending)}</div>
                <p className="text-xs text-gray-600">
                  За выбранный период
                </p>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-medium text-gray-700">Счетов создано</CardTitle>
                <Receipt className="h-5 w-5 text-purple-500" />
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-gray-900 mb-2">{analytics.billsCreated}</div>
                <p className="text-xs text-gray-600">
                  Участвовали в {analytics.billsParticipated}
                </p>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-medium text-gray-700">Баланс</CardTitle>
                <Users className="h-5 w-5 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold mb-2">
                  <span className="text-green-600">+{formatCurrency(analytics.totalOwed)}</span>
                </div>
                <p className="text-xs text-gray-600">
                  Должны: <span className="text-red-600">{formatCurrency(analytics.totalOwing)}</span>
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            {/* Daily Spending Chart */}
            <Card className="lg:col-span-2 border-0 shadow-lg bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center text-xl">
                  <BarChart3 className="h-6 w-6 mr-3 text-blue-600" />
                  Динамика расходов
                </CardTitle>
                <CardDescription className="text-gray-600">
                  Ваши траты по дням за выбранный период
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-2">
                {analytics.dailySpending.length > 0 ? (
                  <ResponsiveContainer width="100%" height={350}>
                    <LineChart
                      data={analytics.dailySpending}
                      margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis
                        dataKey="date"
                        tickFormatter={(value) => {
                          try {
                            return format(parseISO(value), 'dd.MM', { locale: ru })
                          } catch {
                            return value
                          }
                        }}
                        tick={{ fontSize: 11, fill: '#6b7280' }}
                        axisLine={{ stroke: '#e5e7eb' }}
                        tickLine={{ stroke: '#e5e7eb' }}
                        angle={-45}
                        textAnchor="end"
                        height={60}
                        interval={0}
                      />
                      <YAxis
                        tickFormatter={(value) => {
                          if (value >= 1000) {
                            return `${(value / 1000).toFixed(0)}k ₽`
                          }
                          return `${value} ₽`
                        }}
                        tick={{ fontSize: 11, fill: '#6b7280' }}
                        axisLine={{ stroke: '#e5e7eb' }}
                        tickLine={{ stroke: '#e5e7eb' }}
                        width={60}
                      />
                      <Tooltip
                        labelFormatter={formatTooltipDate}
                        formatter={(value: number) => [formatCurrency(value), 'Потрачено']}
                        contentStyle={{
                          backgroundColor: 'white',
                          border: 'none',
                          borderRadius: '12px',
                          boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
                          fontSize: '14px'
                        }}
                        labelStyle={{ color: '#374151', fontWeight: '600' }}
                      />
                      <Line
                        type="monotone"
                        dataKey="amount"
                        stroke="#3B82F6"
                        strokeWidth={3}
                        dot={{ fill: '#3B82F6', strokeWidth: 0, r: 5 }}
                        activeDot={{
                          r: 7,
                          stroke: '#3B82F6',
                          strokeWidth: 3,
                          fill: 'white'
                        }}
                        filter="drop-shadow(0 2px 4px rgba(59, 130, 246, 0.3))"
                      />
                    </LineChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-80 text-gray-500">
                    <div className="text-center">
                      <BarChart3 className="h-16 w-16 mx-auto mb-4 opacity-30" />
                      <p className="text-lg">Нет данных за выбранный период</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Category Spending */}
            <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center text-lg">
                  <PieChartIcon className="h-5 w-5 mr-3 text-purple-600" />
                  Расходы по категориям
                </CardTitle>
                <CardDescription className="text-gray-600">
                  Распределение трат по типам
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-2">
                {analytics.categoryData.length > 0 ? (
                  <div className="space-y-6">
                    <ResponsiveContainer width="100%" height={240}>
                      <PieChart>
                        <Pie
                          data={analytics.categoryData}
                          cx="50%"
                          cy="50%"
                          innerRadius={50}
                          outerRadius={100}
                          paddingAngle={3}
                          dataKey="amount"
                        >
                          {analytics.categoryData.map((entry, index) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={COLORS[index % COLORS.length]}
                              stroke="white"
                              strokeWidth={2}
                            />
                          ))}
                        </Pie>
                        <Tooltip
                          formatter={(value: number) => [formatCurrency(value), 'Потрачено']}
                          contentStyle={{
                            backgroundColor: 'white',
                            border: 'none',
                            borderRadius: '12px',
                            boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
                            fontSize: '14px'
                          }}
                        />
                      </PieChart>
                    </ResponsiveContainer>

                    <div className="space-y-3">
                      {analytics.categoryData.map((category, index) => (
                        <div key={category.category} className="flex items-center justify-between p-3 rounded-xl bg-gray-50/50 hover:bg-gray-100/50 transition-colors">
                          <div className="flex items-center">
                            <div
                              className="w-4 h-4 rounded-full mr-3 shadow-sm"
                              style={{ backgroundColor: COLORS[index % COLORS.length] }}
                            />
                            <span className="text-sm font-medium text-gray-700">{category.category}</span>
                          </div>
                          <span className="text-sm font-bold text-gray-900">{formatCurrency(category.amount)}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-80 text-gray-500">
                    <div className="text-center">
                      <PieChartIcon className="h-16 w-16 mx-auto mb-4 opacity-30" />
                      <p className="text-lg">Нет данных по категориям</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Top Friends */}
            <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center text-lg">
                  <Users className="h-5 w-5 mr-3 text-green-600" />
                  Топ друзей по тратам
                </CardTitle>
                <CardDescription className="text-gray-600">
                  С кем вы чаще всего тратите деньги
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-2">
                {analytics.topFriends.length > 0 ? (
                  <div className="space-y-6">
                    <ResponsiveContainer width="100%" height={240}>
                      <BarChart
                        data={analytics.topFriends}
                        layout="horizontal"
                        margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis
                          type="number"
                          tickFormatter={(value) => {
                            if (value >= 1000) {
                              return `${(value / 1000).toFixed(0)}k ₽`
                            }
                            return `${value} ₽`
                          }}
                          tick={{ fontSize: 11, fill: '#6b7280' }}
                          axisLine={{ stroke: '#e5e7eb' }}
                          tickLine={{ stroke: '#e5e7eb' }}
                        />
                        <YAxis
                          type="category"
                          dataKey="name"
                          tick={{ fontSize: 11, fill: '#6b7280' }}
                          axisLine={{ stroke: '#e5e7eb' }}
                          tickLine={{ stroke: '#e5e7eb' }}
                          width={100}
                        />
                        <Tooltip
                          formatter={(value: number) => [formatCurrency(value), 'Потрачено']}
                          contentStyle={{
                            backgroundColor: 'white',
                            border: 'none',
                            borderRadius: '12px',
                            boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
                            fontSize: '14px'
                          }}
                        />
                        <Bar
                          dataKey="amount"
                          fill="#10B981"
                          radius={[0, 6, 6, 0]}
                          stroke="white"
                          strokeWidth={1}
                        />
                      </BarChart>
                    </ResponsiveContainer>

                    <div className="space-y-3">
                      {analytics.topFriends.map((friend, index) => (
                        <div key={friend.name} className="flex items-center justify-between p-3 rounded-xl bg-gray-50/50 hover:bg-gray-100/50 transition-colors">
                          <div className="flex items-center">
                            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-green-100 text-green-700 text-sm font-bold mr-3">
                              {index + 1}
                            </div>
                            <span className="text-sm font-medium text-gray-700">{friend.name}</span>
                          </div>
                          <span className="text-sm font-bold text-gray-900">{formatCurrency(friend.amount)}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-80 text-gray-500">
                    <div className="text-center">
                      <Users className="h-16 w-16 mx-auto mb-4 opacity-30" />
                      <p className="text-lg">Нет данных по друзьям</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Insights Section */}
          <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-sm">
            <CardHeader className="pb-6">
              <CardTitle className="flex items-center text-xl">
                <Activity className="h-6 w-6 mr-3 text-indigo-600" />
                Финансовые инсайты
              </CardTitle>
              <CardDescription className="text-gray-600 text-base">
                Анализ ваших трат и персональные рекомендации
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-2">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-2xl border border-blue-100/50 shadow-sm">
                  <div className="flex items-center mb-3">
                    <div className="w-10 h-10 bg-blue-500 rounded-xl flex items-center justify-center text-white text-lg mr-3">
                      💰
                    </div>
                    <h4 className="font-bold text-blue-900 text-lg">Общие траты</h4>
                  </div>
                  <p className="text-sm text-blue-800 leading-relaxed">
                    За выбранный период вы потратили <span className="font-semibold">{formatCurrency(analytics.totalSpent)}</span>
                    {analytics.spendingChange > 0 && " - это больше чем в предыдущем периоде"}
                    {analytics.spendingChange < 0 && " - это меньше чем в предыдущем периоде"}
                    {analytics.spendingChange === 0 && " - столько же как в предыдущем периоде"}
                  </p>
                </div>

                <div className="p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-2xl border border-green-100/50 shadow-sm">
                  <div className="flex items-center mb-3">
                    <div className="w-10 h-10 bg-green-500 rounded-xl flex items-center justify-center text-white text-lg mr-3">
                      📊
                    </div>
                    <h4 className="font-bold text-green-900 text-lg">Активность</h4>
                  </div>
                  <p className="text-sm text-green-800 leading-relaxed">
                    Вы создали <span className="font-semibold">{analytics.billsCreated}</span> счет{analytics.billsCreated !== 1 ? 'ов' : ''} и
                    участвовали в <span className="font-semibold">{analytics.billsParticipated}</span> счет{analytics.billsParticipated !== 1 ? 'ах' : 'е'}.
                    {analytics.billsCreated > analytics.billsParticipated && " Вы часто организуете совместные траты!"}
                  </p>
                </div>

                <div className="p-6 bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-2xl border border-purple-100/50 shadow-sm">
                  <div className="flex items-center mb-3">
                    <div className="w-10 h-10 bg-purple-500 rounded-xl flex items-center justify-center text-white text-lg mr-3">
                      🎯
                    </div>
                    <h4 className="font-bold text-purple-900 text-lg">Рекомендации</h4>
                  </div>
                  <p className="text-sm text-purple-800 leading-relaxed">
                    {analytics.avgDailySpending > 1000 && "Рассмотрите возможность планирования бюджета для ежедневных трат."}
                    {analytics.avgDailySpending <= 1000 && analytics.avgDailySpending > 500 && "Ваши ежедневные траты в разумных пределах."}
                    {analytics.avgDailySpending <= 500 && "Отличный контроль над ежедневными тратами!"}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
      </div>
    </div>
  )
}